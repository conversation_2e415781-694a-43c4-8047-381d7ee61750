const { Client } = require('pg')
require('dotenv').config()

async function testDbConnection() {
  const connectionString = process.env.DATABASE_URL

  if (!connectionString) {
    console.error('DATABASE_URL is not set in the .env file.')
    return
  }

  const client = new Client({
    connectionString: connectionString,
  })

  try {
    await client.connect()
    console.log('Successfully connected to PostgreSQL database!')
  } catch (error) {
    console.error('Error connecting to PostgreSQL database:', error.message)
  } finally {
    await client.end()
  }
}

testDbConnection()

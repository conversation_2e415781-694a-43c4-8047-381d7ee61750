generator client {
  provider = "prisma-client-js"
  output   = "./node_modules/.prisma/client_sqlite"
}

datasource db {
  provider = "sqlite"
  url      = "file:../database/db.sqlite"
}

model Language {
  code              String            @id
  name              String
  channels          Channel[]
  country_languages CountryLanguage[]

  @@map("languages")
}

model Country {
  code              String            @id
  name              String
  flag              String?
  channels          Channel[]
  country_languages CountryLanguage[]
  region_countries  RegionCountry[]

  @@map("countries")
}

model CountryLanguage {
  country_code  String
  language_code String
  country       Country @relation(fields: [country_code], references: [code])
  language      Language @relation(fields: [language_code], references: [code])

  @@id([country_code, language_code])
  @@map("country_languages")
}

model Region {
  code             String          @id
  name             String
  region_countries RegionCountry[]

  @@map("regions")
}

model RegionCountry {
  region_code  String
  country_code String
  region       Region @relation(fields: [region_code], references: [code])
  country      Country @relation(fields: [country_code], references: [code])

  @@id([region_code, country_code])
  @@map("region_countries")
}

model Category {
  category_id Int       @id @default(autoincrement())
  name        String
  is_active   Boolean   @default(true)
  channels    Channel[]

  @@map("categories")
}

model Channel {
  channel_id    String         @id
  name          String
  alt_names     String?
  country_code  String?
  category_id   Int?
  logo_url      String?
  is_active     Boolean        @default(true)
  created_at    String
  language_code String?
  group_id      Int?
  country       Country?       @relation(fields: [country_code], references: [code])
  category      Category?      @relation(fields: [category_id], references: [category_id])
  language      Language?      @relation(fields: [language_code], references: [code])
  group         Group?         @relation(fields: [group_id], references: [id])
  channel_sources ChannelSource[]

  @@map("channels")
}

model SourceProvider {
  provider_id Int             @id @default(autoincrement())
  name        String
  priority    Int             @default(0)
  is_active   Boolean         @default(true)
  channel_sources ChannelSource[]

  @@map("source_providers")
}

model ChannelSource {
  source_id    Int            @id @default(autoincrement())
  channel_id   String
  provider_id  Int?
  source_url   String
  priority     Int            @default(0)
  is_active    Boolean        @default(true)
  last_checked String?
  last_status  Boolean?
  retry_count  Int            @default(0)
  is_external  Boolean        @default(false)
  channel      Channel        @relation(fields: [channel_id], references: [channel_id])
  source_provider SourceProvider? @relation(fields: [provider_id], references: [provider_id])

  @@map("channel_sources")
}

model Provider {
  provider_id Int    @id @default(autoincrement())
  name        String

  @@map("providers")
}

model Group {
  id          Int       @id @default(autoincrement())
  name        String
  is_featured Boolean   @default(false)
  channels    Channel[]

  @@map("groups")
}

model DBVersion {
  version Int @id

  @@map("db_version")
}

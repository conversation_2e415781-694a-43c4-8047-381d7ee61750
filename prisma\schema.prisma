generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Language {
  code              String            @id @db.Text
  name              String            @db.Text
  channels          Channel[]
  country_languages CountryLanguage[]

  @@map("languages")
}

model Country {
  code              String            @id @db.Text
  name              String            @db.Text
  flag              String?           @db.Text
  channels          Channel[]
  country_languages CountryLanguage[]
  region_countries  RegionCountry[]

  @@map("countries")
}

model CountryLanguage {
  country_code  String @db.Text
  language_code String @db.Text
  country       Country @relation(fields: [country_code], references: [code])
  language      Language @relation(fields: [language_code], references: [code])

  @@id([country_code, language_code])
  @@map("country_languages")
}

model Region {
  code             String          @id @db.Text
  name             String          @db.Text
  region_countries RegionCountry[]

  @@map("regions")
}

model RegionCountry {
  region_code  String @db.Text
  country_code String @db.Text
  region       Region @relation(fields: [region_code], references: [code])
  country      Country @relation(fields: [country_code], references: [code])

  @@id([region_code, country_code])
  @@map("region_countries")
}

model Category {
  category_id Int       @id @default(autoincrement())
  name        String    @db.Text
  is_active   Boolean   @default(true)
  channels    Channel[]

  @@map("categories")
}

model Channel {
  channel_id    String         @id @db.Text
  name          String         @db.Text
  alt_names     String?        @db.Text
  country_code  String?        @db.Text
  category_id   Int?
  logo_url      String?        @db.Text
  is_active     Boolean        @default(true)
  created_at    DateTime       @default(now()) @db.Timestamptz(6)
  language_code String?        @db.Text
  group_id      Int?
  country       Country?       @relation(fields: [country_code], references: [code])
  category      Category?      @relation(fields: [category_id], references: [category_id])
  language      Language?      @relation(fields: [language_code], references: [code])
  group         Group?         @relation(fields: [group_id], references: [id])
  channel_sources ChannelSource[]

  @@map("channels")
}

model SourceProvider {
  provider_id Int             @id @default(autoincrement())
  name        String          @db.Text
  priority    Int             @default(0)
  is_active   Boolean         @default(true)
  channel_sources ChannelSource[]

  @@map("source_providers")
}

model ChannelSource {
  source_id    Int            @id @default(autoincrement())
  channel_id   String
  provider_id  Int?
  source_url   String         @db.Text
  priority     Int            @default(0)
  is_active    Boolean        @default(true)
  last_checked DateTime?      @db.Timestamptz(6)
  last_status  Boolean?
  retry_count  Int            @default(0)
  is_external  Boolean        @default(false)
  channel      Channel        @relation(fields: [channel_id], references: [channel_id])
  source_provider SourceProvider? @relation(fields: [provider_id], references: [provider_id])

  @@map("channel_sources")
}

model Provider {
  provider_id Int    @id @default(autoincrement())
  name        String @db.Text

  @@map("providers")
}

model Group {
  id          Int       @id @default(autoincrement())
  name        String    @db.Text
  is_featured Boolean   @default(false)
  channels    Channel[]

  @@map("groups")
}

model DBVersion {
  version Int @id

  @@map("db_version")
}

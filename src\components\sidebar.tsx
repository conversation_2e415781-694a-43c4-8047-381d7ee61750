'use client'
import {
  IconDatabase,
  IconSettings,
  IconDashboard,
  IconFileDescription,
  IconPlus,
  IconMenu2,
} from '@tabler/icons-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'

export function Sidebar({ isMobile }: { isMobile?: boolean }) {
  const pathname = usePathname()

  const navItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: IconDashboard,
    },
    {
      name: 'Channels',
      href: '/dashboard/channels',
      icon: IconDatabase,
    },
    {
      name: 'Reports',
      href: '/dashboard/reports',
      icon: IconFileDescription,
    },
    {
      name: 'Settings',
      href: '/dashboard/settings',
      icon: IconSettings,
    },
  ]

  return (
    <>
      {isMobile ? (
        <nav className="grid gap-2 text-lg font-medium">
          {navItems.map(item => (
            <Link
              key={item.name}
              href={item.href}
              className={`mx-[-0.65rem] flex items-center gap-4 rounded-xl px-3 py-2 text-muted-foreground hover:text-foreground ${
                pathname === item.href && 'bg-muted text-foreground'
              }`}
            >
              <item.icon className="h-5 w-5" />
              {item.name}
            </Link>
          ))}
        </nav>
      ) : (
        <div className="hidden border-r bg-muted/40 md:block">
          <div className="flex h-full max-h-screen flex-col gap-2">
            <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 font-semibold"
              >
                <IconDatabase className="h-6 w-6" />
                <span className="">Channel AP Manager</span>
              </Link>
            </div>
            <div className="flex-1">
              <nav className="grid items-start px-2 text-sm font-medium lg:px-4">
                {navItems.map(item => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={`flex items-center gap-3 rounded-lg px-3 py-2 text-muted-foreground transition-all hover:text-foreground ${
                      pathname === item.href && 'bg-muted text-foreground'
                    }`}
                  >
                    <item.icon className="h-4 w-4" />
                    {item.name}
                  </Link>
                ))}
              </nav>
            </div>
            <div className="mt-auto p-4">
              {/* Floating Action Button */}
              <Button className="w-full">
                <IconPlus className="mr-2 h-4 w-4" />
                New Action
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

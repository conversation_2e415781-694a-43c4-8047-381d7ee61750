const { Client } = require('pg')
require('dotenv').config()

async function testSqliteConnection() {
  try {
    // Using the MCP tool to list tables in the SQLite database
    const result = await use_mcp_tool(
      'github.com/modelcontextprotocol/servers/tree/main/src/sqlite',
      'list_tables',
      {}
    )
    console.log(
      'Successfully connected to SQLite database and listed tables:',
      result
    )
  } catch (error) {
    console.error('Error connecting to SQLite database:', error.message)
  }
}

testSqliteConnection()

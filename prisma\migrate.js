const { PrismaClient: PrismaClientPg } = require('@prisma/client')
const {
  PrismaClient: PrismaClientSqlite,
} = require('./node_modules/.prisma/client_sqlite')

// Mapping functions to handle code format differences
function normalizeCountryCode(code) {
  if (!code) return null
  return code.toUpperCase() // Convert to uppercase for PostgreSQL
}

function normalizeLanguageCode(code) {
  if (!code) return null

  // Map 2-letter codes to 3-letter ISO codes
  const languageMap = {
    // Common 2-letter to 3-letter mappings
    pl: 'pol', // Polish
    ru: 'rus', // Russian
    FR: 'fra', // French (uppercase)
    fr: 'fra', // French (lowercase)
    de: 'deu', // German
    DE: 'deu', // German (uppercase)
    en: 'eng', // English
    EN: 'eng', // English (uppercase)
    es: 'spa', // Spanish
    ES: 'spa', // Spanish (uppercase)
    it: 'ita', // Italian
    IT: 'ita', // Italian (uppercase)
    pt: 'por', // Portuguese
    PT: 'por', // Portuguese (uppercase)
    ar: 'ara', // Arabic
    AR: 'ara', // Arabic (uppercase)
    zh: 'zho', // Chinese
    ZH: 'zho', // Chinese (uppercase)
    ja: 'jpn', // Japanese
    JA: 'jpn', // Japanese (uppercase)
    ko: 'kor', // Korean
    KO: 'kor', // Korean (uppercase)
    hi: 'hin', // Hindi
    HI: 'hin', // Hindi (uppercase)
    tr: 'tur', // Turkish
    TR: 'tur', // Turkish (uppercase)
    nl: 'nld', // Dutch
    NL: 'nld', // Dutch (uppercase)
    sv: 'swe', // Swedish
    SV: 'swe', // Swedish (uppercase)
    da: 'dan', // Danish
    DA: 'dan', // Danish (uppercase)
    no: 'nor', // Norwegian
    NO: 'nor', // Norwegian (uppercase)
    fi: 'fin', // Finnish
    FI: 'fin', // Finnish (uppercase)
    cs: 'ces', // Czech
    CS: 'ces', // Czech (uppercase)
    sk: 'slk', // Slovak
    SK: 'slk', // Slovak (uppercase)
    hu: 'hun', // Hungarian
    HU: 'hun', // Hungarian (uppercase)
    ro: 'ron', // Romanian
    RO: 'ron', // Romanian (uppercase)
    bg: 'bul', // Bulgarian
    BG: 'bul', // Bulgarian (uppercase)
    hr: 'hrv', // Croatian
    HR: 'hrv', // Croatian (uppercase)
    sr: 'srp', // Serbian
    SR: 'srp', // Serbian (uppercase)
    sl: 'slv', // Slovenian
    SL: 'slv', // Slovenian (uppercase)
    et: 'est', // Estonian
    ET: 'est', // Estonian (uppercase)
    lv: 'lav', // Latvian
    LV: 'lav', // Latvian (uppercase)
    lt: 'lit', // Lithuanian
    LT: 'lit', // Lithuanian (uppercase)
    el: 'ell', // Greek
    EL: 'ell', // Greek (uppercase)
    he: 'heb', // Hebrew
    HE: 'heb', // Hebrew (uppercase)
    fa: 'fas', // Persian
    FA: 'fas', // Persian (uppercase)
    ur: 'urd', // Urdu
    UR: 'urd', // Urdu (uppercase)
    th: 'tha', // Thai
    TH: 'tha', // Thai (uppercase)
    vi: 'vie', // Vietnamese
    VI: 'vie', // Vietnamese (uppercase)
    id: 'ind', // Indonesian
    ID: 'ind', // Indonesian (uppercase)
    ms: 'msa', // Malay
    MS: 'msa', // Malay (uppercase)
    tl: 'fil', // Filipino
    TL: 'fil', // Filipino (uppercase)
    uk: 'ukr', // Ukrainian
    UK: 'ukr', // Ukrainian (uppercase)
    be: 'bel', // Belarusian
    BE: 'bel', // Belarusian (uppercase)
    ka: 'kat', // Georgian
    KA: 'kat', // Georgian (uppercase)
    hy: 'hye', // Armenian
    HY: 'hye', // Armenian (uppercase)
    az: 'aze', // Azerbaijani
    AZ: 'aze', // Azerbaijani (uppercase)
    kk: 'kaz', // Kazakh
    KK: 'kaz', // Kazakh (uppercase)
    ky: 'kir', // Kyrgyz
    KY: 'kir', // Kyrgyz (uppercase)
    uz: 'uzb', // Uzbek
    UZ: 'uzb', // Uzbek (uppercase)
    tg: 'tgk', // Tajik
    TG: 'tgk', // Tajik (uppercase)
    mn: 'mon', // Mongolian
    MN: 'mon', // Mongolian (uppercase)
  }

  return languageMap[code] || code // Return mapped code or original if no mapping
}

const prismaPg = new PrismaClientPg()
const prismaSqlite = new PrismaClientSqlite({
  datasources: {
    db: {
      url: 'file:../database/db.sqlite', // Corrected path to SQLite DB
    },
  },
})

async function main() {
  try {
    // Migrate Languages
    const languages = await prismaSqlite.language.findMany()
    for (const lang of languages) {
      await prismaPg.language.upsert({
        where: { code: lang.code },
        update: {},
        create: lang,
      })
    }
    console.log('Languages migrated.')

    // Migrate Countries
    const countries = await prismaSqlite.country.findMany()
    for (const country of countries) {
      await prismaPg.country.upsert({
        where: { code: country.code },
        update: {},
        create: country,
      })
    }
    console.log('Countries migrated.')

    // Migrate CountryLanguages
    const countryLanguages = await prismaSqlite.countryLanguage.findMany()
    for (const cl of countryLanguages) {
      const countryExists = await prismaPg.country.findUnique({
        where: { code: cl.country_code },
      })
      const languageExists = await prismaPg.language.findUnique({
        where: { code: cl.language_code },
      })

      if (countryExists && languageExists) {
        await prismaPg.countryLanguage.upsert({
          where: {
            country_code_language_code: {
              country_code: cl.country_code,
              language_code: cl.language_code,
            },
          },
          update: {},
          create: cl,
        })
      } else {
        console.warn(
          `Skipping CountryLanguage migration for country_code: ${cl.country_code}, language_code: ${cl.language_code} due to missing foreign key.`
        )
      }
    }
    console.log('CountryLanguages migrated.')

    // Migrate Regions
    const regions = await prismaSqlite.region.findMany()
    for (const region of regions) {
      await prismaPg.region.upsert({
        where: { code: region.code },
        update: {},
        create: region,
      })
    }
    console.log('Regions migrated.')

    // Migrate RegionCountries
    const regionCountries = await prismaSqlite.regionCountry.findMany()
    for (const rc of regionCountries) {
      await prismaPg.regionCountry.upsert({
        where: {
          region_code_country_code: {
            region_code: rc.region_code,
            country_code: rc.country_code,
          },
        },
        update: {},
        create: rc,
      })
    }
    console.log('RegionCountries migrated.')

    // Migrate Categories
    const categories = await prismaSqlite.category.findMany()
    for (const category of categories) {
      await prismaPg.category.upsert({
        where: { category_id: category.category_id },
        update: {},
        create: category,
      })
    }
    console.log('Categories migrated.')

    // Migrate SourceProviders
    const sourceProviders = await prismaSqlite.sourceProvider.findMany()
    for (const sp of sourceProviders) {
      await prismaPg.sourceProvider.upsert({
        where: { provider_id: sp.provider_id },
        update: {},
        create: sp,
      })
    }
    console.log('SourceProviders migrated.')

    // Migrate Providers
    const providers = await prismaSqlite.provider.findMany()
    for (const provider of providers) {
      await prismaPg.provider.upsert({
        where: { provider_id: provider.provider_id },
        update: {},
        create: provider,
      })
    }
    console.log('Providers migrated.')

    // Migrate Groups
    const groups = await prismaSqlite.group.findMany()
    for (const group of groups) {
      await prismaPg.group.upsert({
        where: { id: group.id },
        update: {},
        create: group,
      })
    }
    console.log('Groups migrated.')

    // Migrate Channels (after related tables)
    const channels = await prismaSqlite.channel.findMany()
    for (const channel of channels) {
      // Normalize codes for PostgreSQL format
      const normalizedCountryCode = normalizeCountryCode(channel.country_code)
      const normalizedLanguageCode = normalizeLanguageCode(
        channel.language_code
      )

      const countryExists = normalizedCountryCode
        ? await prismaPg.country.findUnique({
            where: { code: normalizedCountryCode },
          })
        : true
      const categoryExists = channel.category_id
        ? await prismaPg.category.findUnique({
            where: { category_id: channel.category_id },
          })
        : true
      const languageExists = normalizedLanguageCode
        ? await prismaPg.language.findUnique({
            where: { code: normalizedLanguageCode },
          })
        : true
      const groupExists = channel.group_id
        ? await prismaPg.group.findUnique({ where: { id: channel.group_id } })
        : true

      if (countryExists && categoryExists && languageExists && groupExists) {
        await prismaPg.channel.upsert({
          where: { channel_id: channel.channel_id },
          update: {},
          create: {
            channel_id: channel.channel_id,
            name: channel.name,
            alt_names: channel.alt_names,
            country_code: normalizedCountryCode,
            category_id: channel.category_id,
            logo_url: channel.logo_url,
            is_active: channel.is_active,
            created_at:
              channel.created_at && !isNaN(new Date(channel.created_at))
                ? new Date(channel.created_at).toISOString()
                : undefined,
            language_code: normalizedLanguageCode,
            group_id: channel.group_id,
          },
        })
      } else {
        console.warn(
          `Skipping Channel migration for channel_id: ${
            channel.channel_id
          } due to missing foreign key. Country: ${
            countryExists ? 'OK' : 'MISSING'
          }, Category: ${categoryExists ? 'OK' : 'MISSING'}, Language: ${
            languageExists ? 'OK' : 'MISSING'
          }, Group: ${groupExists ? 'OK' : 'MISSING'}`
        )
      }
    }
    console.log('Channels migrated.')

    // Migrate ChannelSources (after channels and source_providers)
    const channelSources = await prismaSqlite.channelSource.findMany()
    for (const cs of channelSources) {
      const channelExists = await prismaPg.channel.findUnique({
        where: { channel_id: cs.channel_id },
      })

      // Check if provider exists, if not use default provider_id = 1
      let validProviderId = cs.provider_id
      const sourceProviderExists = cs.provider_id
        ? await prismaPg.sourceProvider.findUnique({
            where: { provider_id: cs.provider_id },
          })
        : null

      // If provider doesn't exist, use default provider_id = 1
      if (!sourceProviderExists && cs.provider_id) {
        console.warn(
          `Provider ${cs.provider_id} not found for source ${cs.source_id}, using default provider_id = 1`
        )
        validProviderId = 1
      }

      if (channelExists) {
        await prismaPg.channelSource.upsert({
          where: { source_id: cs.source_id },
          update: {},
          create: {
            source_id: cs.source_id,
            channel_id: cs.channel_id,
            provider_id: validProviderId,
            source_url: cs.source_url,
            priority: cs.priority,
            is_active: cs.is_active,
            last_checked:
              cs.last_checked && !isNaN(new Date(cs.last_checked))
                ? new Date(cs.last_checked).toISOString()
                : null,
            last_status: cs.last_status,
            retry_count: cs.retry_count,
            is_external: cs.is_external,
          },
        })
      } else {
        console.warn(
          `Skipping ChannelSource migration for source_id: ${cs.source_id} due to missing channel: ${cs.channel_id}`
        )
      }
    }
    console.log('ChannelSources migrated.')

    // Migrate DBVersion
    const dbVersion = await prismaSqlite.dBVersion.findMany()
    for (const version of dbVersion) {
      await prismaPg.dBVersion.upsert({
        where: { version: version.version },
        update: {},
        create: version,
      })
    }
    console.log('DBVersion migrated.')
  } catch (error) {
    console.error('Error during migration:', error)
  } finally {
    await prismaPg.$disconnect()
    await prismaSqlite.$disconnect()
  }
}

main()

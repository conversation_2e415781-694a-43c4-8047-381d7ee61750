import { Sidebar } from '@/components/sidebar'
import { ModeToggle } from '@/components/mode-toggle'
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from '@/components/ui/sheet'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { IconDatabase, IconMenu2, IconPlus } from '@tabler/icons-react'

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="grid min-h-screen w-full md:grid-cols-[220px_1fr] lg:grid-cols-[280px_1fr]">
      <Sidebar />
      <div className="flex flex-col">
        <header className="flex h-14 items-center gap-4 border-b bg-muted/40 px-4 lg:h-[60px] lg:px-6">
          <Sheet>
            <SheetTrigger asChild>
              <Button
                variant="outline"
                size="icon"
                className="shrink-0 md:hidden"
              >
                <IconMenu2 className="h-5 w-5" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="flex flex-col">
              <Link
                href="/dashboard"
                className="flex items-center gap-2 text-lg font-semibold"
              >
                <IconDatabase className="h-6 w-6" />
                <span>Channel AP Manager</span>
              </Link>
              <Sidebar isMobile={true} />
            </SheetContent>
          </Sheet>
          <div className="w-full flex-1">
            {/* Search or other header content can go here */}
          </div>
          <div className="flex items-center gap-4">
            <Button className="hidden md:flex">
              <IconPlus className="mr-2 h-4 w-4" />
              New Action
            </Button>
            <ModeToggle />
          </div>
        </header>
        <main className="flex flex-1 flex-col gap-4 p-4 lg:gap-6 lg:p-6">
          {children}
        </main>
      </div>
    </div>
  )
}

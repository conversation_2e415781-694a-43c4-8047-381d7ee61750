const fs = require('fs')
const path = require('path')
const {
  PrismaClient: PrismaClientSqlite,
} = require('../../prisma/node_modules/.prisma/client_sqlite')

async function addLanguagesToSqlite() {
  const prismaSqlite = new PrismaClientSqlite({
    datasources: {
      db: {
        url: 'file:../database/db.sqlite',
      },
    },
  })

  try {
    const languagesPath = path.join(__dirname, '../../data/languages.json')
    const languagesData = JSON.parse(fs.readFileSync(languagesPath, 'utf8'))

    console.log(`Found ${languagesData.length} languages in JSON file`)
    console.log('Connected to SQLite database...')

    // Disable foreign key constraints temporarily
    await prismaSqlite.$executeRaw`PRAGMA foreign_keys = OFF;`
    console.log('Disabled foreign key constraints')

    // Check current count
    const currentCount = await prismaSqlite.language.count()
    console.log(`Current languages in database: ${currentCount}`)

    let insertedCount = 0
    let updatedCount = 0
    let errorCount = 0

    // Process languages in batches for better performance
    const batchSize = 100
    for (let i = 0; i < languagesData.length; i += batchSize) {
      const batch = languagesData.slice(i, i + batchSize)

      console.log(
        `Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
          languagesData.length / batchSize
        )} (${batch.length} languages)`
      )

      for (const lang of batch) {
        try {
          // Check if language exists
          const existing = await prismaSqlite.language.findUnique({
            where: { code: lang.code },
          })

          if (existing) {
            // Update existing language
            await prismaSqlite.language.update({
              where: { code: lang.code },
              data: { name: lang.name },
            })
            updatedCount++
          } else {
            // Create new language
            await prismaSqlite.language.create({
              data: {
                code: lang.code,
                name: lang.name,
              },
            })
            insertedCount++
          }
        } catch (error) {
          console.error(
            `Error processing language ${lang.code} (${lang.name}):`,
            error.message
          )
          errorCount++
        }
      }
    }

    // Re-enable foreign key constraints
    await prismaSqlite.$executeRaw`PRAGMA foreign_keys = ON;`
    console.log('Re-enabled foreign key constraints')

    // Verify final count
    const finalCount = await prismaSqlite.language.count()

    console.log('\n=== SUMMARY ===')
    console.log(`Languages inserted: ${insertedCount}`)
    console.log(`Languages updated: ${updatedCount}`)
    console.log(`Errors: ${errorCount}`)
    console.log(`Total languages processed: ${insertedCount + updatedCount}`)
    console.log(`Final count in database: ${finalCount}`)
    console.log('All languages added to db.sqlite successfully.')
  } catch (error) {
    console.error('Error adding languages to SQLite:', error)
  } finally {
    await prismaSqlite.$disconnect()
  }
}

addLanguagesToSqlite()

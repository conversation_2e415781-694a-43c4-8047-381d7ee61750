#!/usr/bin/env python3
"""
SQLite to PostgreSQL Migration Script
Transfers all data from db.sqlite to PostgreSQL database.
Foreign key constraints are added at the end to avoid constraint violations during data transfer.
"""

import sqlite3
import psycopg2
from psycopg2.extras import RealDictCursor
import os
from datetime import datetime
import sys
import json

# Load environment variables from .env file
def load_env():
    """Load environment variables from .env file"""
    try:
        with open('.env', 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    except FileNotFoundError:
        pass

load_env()

# Database configuration
SQLITE_DB_PATH = "database/db.sqlite"
POSTGRES_CONNECTION_STRING = os.getenv("DATABASE_URL", "postgresql://username:password@localhost:5432/database_name")

# Default provider ID for missing providers (based on your memory)
DEFAULT_PROVIDER_ID = 1

# File to store skipped channel_sources rows
SKIPPED_CHANNELS_FILE = "skipped_channel_sources.json"

def connect_sqlite():
    """Connect to SQLite database"""
    try:
        conn = sqlite3.connect(SQLITE_DB_PATH)
        conn.row_factory = sqlite3.Row
        return conn
    except Exception as e:
        print(f"Error connecting to SQLite: {e}")
        sys.exit(1)

def connect_postgres():
    """Connect to PostgreSQL database"""
    try:
        conn = psycopg2.connect(POSTGRES_CONNECTION_STRING)
        conn.autocommit = False
        return conn
    except Exception as e:
        print(f"Error connecting to PostgreSQL: {e}")
        print("Make sure your DATABASE_URL environment variable is set correctly")
        sys.exit(1)

def normalize_country_code(code):
    """Normalize country code to uppercase 2-letter format"""
    if not code:
        return None
    return code.upper()[:2] if len(code) >= 2 else code.upper()

def normalize_language_code(code):
    """Normalize language code to consistent format"""
    if not code:
        return None
    return code.lower()

def drop_foreign_key_constraints(pg_conn):
    """Drop all foreign key constraints to allow data insertion"""
    print("Dropping foreign key constraints...")

    constraints_to_drop = [
        ("country_languages", "country_languages_country_code_fkey"),
        ("country_languages", "country_languages_language_code_fkey"),
        ("region_countries", "region_countries_region_code_fkey"),
        ("region_countries", "region_countries_country_code_fkey"),
        ("channels", "channels_country_code_fkey"),
        ("channels", "channels_category_id_fkey"),
        ("channels", "channels_language_code_fkey"),
        ("channels", "channels_group_id_fkey"),
        ("channel_sources", "channel_sources_channel_id_fkey"),
        ("channel_sources", "channel_sources_provider_id_fkey"),
    ]

    with pg_conn.cursor() as cursor:
        for table, constraint in constraints_to_drop:
            try:
                cursor.execute(f"ALTER TABLE {table} DROP CONSTRAINT IF EXISTS {constraint}")
                print(f"  Dropped constraint {constraint} from {table}")
            except Exception as e:
                print(f"  Warning: Could not drop constraint {constraint}: {e}")

    pg_conn.commit()

def create_foreign_key_constraints(pg_conn):
    """Create foreign key constraints after data migration"""
    print("Creating foreign key constraints...")

    constraints = [
        """ALTER TABLE country_languages
           ADD CONSTRAINT country_languages_country_code_fkey
           FOREIGN KEY (country_code) REFERENCES countries(code)""",

        """ALTER TABLE country_languages
           ADD CONSTRAINT country_languages_language_code_fkey
           FOREIGN KEY (language_code) REFERENCES languages(code)""",

        """ALTER TABLE region_countries
           ADD CONSTRAINT region_countries_region_code_fkey
           FOREIGN KEY (region_code) REFERENCES regions(code)""",

        """ALTER TABLE region_countries
           ADD CONSTRAINT region_countries_country_code_fkey
           FOREIGN KEY (country_code) REFERENCES countries(code)""",

        """ALTER TABLE channels
           ADD CONSTRAINT channels_country_code_fkey
           FOREIGN KEY (country_code) REFERENCES countries(code)""",

        """ALTER TABLE channels
           ADD CONSTRAINT channels_category_id_fkey
           FOREIGN KEY (category_id) REFERENCES categories(category_id)""",

        """ALTER TABLE channels
           ADD CONSTRAINT channels_language_code_fkey
           FOREIGN KEY (language_code) REFERENCES languages(code)""",

        """ALTER TABLE channels
           ADD CONSTRAINT channels_group_id_fkey
           FOREIGN KEY (group_id) REFERENCES groups(id)""",

        """ALTER TABLE channel_sources
           ADD CONSTRAINT channel_sources_channel_id_fkey
           FOREIGN KEY (channel_id) REFERENCES channels(channel_id)""",

        """ALTER TABLE channel_sources
           ADD CONSTRAINT channel_sources_provider_id_fkey
           FOREIGN KEY (provider_id) REFERENCES source_providers(provider_id)""",
    ]

    with pg_conn.cursor() as cursor:
        for constraint_sql in constraints:
            try:
                cursor.execute(constraint_sql)
                constraint_name = constraint_sql.split("CONSTRAINT ")[1].split(" ")[0]
                print(f"  Created constraint {constraint_name}")
            except Exception as e:
                print(f"  Warning: Could not create constraint: {e}")

    pg_conn.commit()

def clear_postgres_tables(pg_conn):
    """Clear all tables in PostgreSQL (in correct order to avoid FK violations)"""
    print("Clearing PostgreSQL tables...")

    tables_to_clear = [
        "channel_sources",
        "country_languages",
        "region_countries",
        "channels",
        "categories",
        "source_providers",
        "providers",
        "groups",
        "countries",
        "languages",
        "regions",
        "db_version"
    ]

    with pg_conn.cursor() as cursor:
        for table in tables_to_clear:
            cursor.execute(f"DELETE FROM {table}")
            print(f"  Cleared table {table}")

    pg_conn.commit()

def migrate_table(sqlite_conn, pg_conn, table_name, transform_func=None):
    """Migrate data from SQLite table to PostgreSQL table"""
    print(f"Migrating table: {table_name}")

    # Get data from SQLite
    sqlite_cursor = sqlite_conn.cursor()
    sqlite_cursor.execute(f"SELECT * FROM {table_name}")
    rows = sqlite_cursor.fetchall()

    if not rows:
        print(f"  No data found in {table_name}")
        return

    # Get column names
    columns = [description[0] for description in sqlite_cursor.description]

    # Prepare PostgreSQL insert
    placeholders = ', '.join(['%s'] * len(columns))
    columns_str = ', '.join(columns)
    insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"

    # Transform and insert data
    pg_cursor = pg_conn.cursor()
    inserted_count = 0

    for row in rows:
        try:
            # Convert row to list for modification
            row_data = list(row)

            # Apply transformation if provided
            if transform_func:
                transformed_row_data = transform_func(row_data, columns)
                if transformed_row_data is None:
                    print(f"  Skipping row in {table_name} due to transformation rule: {row_data}")
                    continue
                row_data = transformed_row_data

            pg_cursor.execute(insert_sql, row_data)
            inserted_count += 1

        except Exception as e:
            print(f"  Error inserting row into {table_name}: {e}")
            print(f"  Row data: {row_data}")
            continue

    pg_conn.commit()
    print(f"  Migrated {inserted_count}/{len(rows)} rows")

def convert_boolean_fields(row_data, columns, boolean_fields):
    """Convert integer boolean fields (0/1) to actual boolean values"""
    for field in boolean_fields:
        if field in columns:
            idx = columns.index(field)
            if row_data[idx] is not None:
                row_data[idx] = bool(row_data[idx])
    return row_data

def transform_channels(row_data, columns):
    """Transform channels data during migration"""
    # Find column indices
    country_code_idx = columns.index('country_code') if 'country_code' in columns else -1
    language_code_idx = columns.index('language_code') if 'language_code' in columns else -1
    created_at_idx = columns.index('created_at') if 'created_at' in columns else -1

    # Convert boolean fields
    row_data = convert_boolean_fields(row_data, columns, ['is_active'])

    # Normalize country code
    if country_code_idx >= 0 and row_data[country_code_idx]:
        row_data[country_code_idx] = normalize_country_code(row_data[country_code_idx])

    # Normalize language code
    if language_code_idx >= 0 and row_data[language_code_idx]:
        # Specific fix for 'pl' to 'pol' as requested
        if row_data[language_code_idx].lower() == 'pl':
            row_data[language_code_idx] = 'pol'
        elif row_data[language_code_idx].lower() == 'ru':
            row_data[language_code_idx] = 'rus'
        elif row_data[language_code_idx].lower() == 'fr':
            row_data[language_code_idx] = 'fra'
        elif row_data[language_code_idx].lower() == 'de':
            row_data[language_code_idx] = 'ger'
        row_data[language_code_idx] = normalize_language_code(row_data[language_code_idx])

    # Convert created_at from string to timestamp
    if created_at_idx >= 0 and row_data[created_at_idx]:
        try:
            # Try to parse the datetime string
            dt = datetime.fromisoformat(row_data[created_at_idx].replace('Z', '+00:00'))
            row_data[created_at_idx] = dt
        except:
            # If parsing fails, use current timestamp
            row_data[created_at_idx] = datetime.now()

    return row_data

def transform_channel_sources(row_data, columns):
    """Transform channel_sources data during migration"""
    channel_id_idx = columns.index('channel_id') if 'channel_id' in columns else -1
    provider_id_idx = columns.index('provider_id') if 'provider_id' in columns else -1
    last_checked_idx = columns.index('last_checked') if 'last_checked' in columns else -1

    # Convert boolean fields
    row_data = convert_boolean_fields(row_data, columns, ['is_active', 'last_status', 'is_external'])

    # Ensure channel_id is a string
    if channel_id_idx >= 0 and row_data[channel_id_idx] is not None:
        row_data[channel_id_idx] = str(row_data[channel_id_idx])

    # Set default provider for missing providers
    if provider_id_idx >= 0 and not row_data[provider_id_idx]:
        row_data[provider_id_idx] = DEFAULT_PROVIDER_ID

    # Convert last_checked from string to timestamp
    if last_checked_idx >= 0 and row_data[last_checked_idx]:
        try:
            dt = datetime.fromisoformat(row_data[last_checked_idx].replace('Z', '+00:00'))
            row_data[last_checked_idx] = dt
        except ValueError: # Catch specific ValueError for fromisoformat
            row_data[last_checked_idx] = None
        except TypeError: # Catch TypeError if row_data[last_checked_idx] is not a string
            row_data[last_checked_idx] = None
    elif last_checked_idx >= 0 and row_data[last_checked_idx] == '': # Handle empty string explicitly
        row_data[last_checked_idx] = None

    return row_data

def transform_countries(row_data, columns):
    """Transform countries data during migration"""
    code_idx = columns.index('code') if 'code' in columns else -1

    # Normalize country code
    if code_idx >= 0 and row_data[code_idx]:
        row_data[code_idx] = normalize_country_code(row_data[code_idx])

    return row_data

def transform_languages(row_data, columns):
    """Transform languages data during migration"""
    code_idx = columns.index('code') if 'code' in columns else -1

    # Normalize language code
    if code_idx >= 0 and row_data[code_idx]:
        # Apply specific fixes for language codes
        if row_data[code_idx].lower() == 'pl':
            row_data[code_idx] = 'pol'
        elif row_data[code_idx].lower() == 'ru':
            row_data[code_idx] = 'rus'
        elif row_data[code_idx].lower() == 'fr':
            row_data[code_idx] = 'fra'
        row_data[code_idx] = normalize_language_code(row_data[code_idx])

    return row_data

def transform_categories(row_data, columns):
    """Transform categories data during migration"""
    # Convert boolean fields
    row_data = convert_boolean_fields(row_data, columns, ['is_active'])
    return row_data

def transform_groups(row_data, columns):
    """Transform groups data during migration"""
    # Convert boolean fields
    row_data = convert_boolean_fields(row_data, columns, ['is_featured'])
    return row_data

def transform_source_providers(row_data, columns):
    """Transform source_providers data during migration"""
    # Convert boolean fields
    row_data = convert_boolean_fields(row_data, columns, ['is_active'])
    return row_data

def main():
    print("Starting SQLite to PostgreSQL migration...")

    # Connect to databases
    sqlite_conn = connect_sqlite()
    pg_conn = connect_postgres()

    try:
        # Step 1: Drop foreign key constraints
        drop_foreign_key_constraints(pg_conn)

        # Step 2: Clear PostgreSQL tables
        clear_postgres_tables(pg_conn)

        # Step 3: Ensure default provider exists
        print("Ensuring default provider exists...")
        with pg_conn.cursor() as cursor:
            cursor.execute("INSERT INTO source_providers (provider_id, name, priority, is_active) VALUES (%s, %s, %s, %s) ON CONFLICT (provider_id) DO NOTHING",
                         (DEFAULT_PROVIDER_ID, "Default Provider", 0, True))
        pg_conn.commit()

        # Step 4: Migrate data (order matters due to dependencies)
        migration_order = [
            ("languages", transform_languages),
            ("countries", transform_countries),
            ("regions", None),
            ("categories", transform_categories),
            ("groups", transform_groups),
            ("providers", None),
            ("source_providers", transform_source_providers),
            ("db_version", None),
            ("country_languages", None),
            ("region_countries", None),
            ("channels", transform_channels),
            ("channel_sources", transform_channel_sources),
        ]

        # Step 4: Migrate data (order matters due to dependencies)
        migration_order = [
            ("languages", transform_languages),
            ("countries", transform_countries),
            ("regions", None),
            ("categories", transform_categories),
            ("groups", transform_groups),
            ("providers", None),
            ("source_providers", transform_source_providers),
            ("db_version", None),
            ("country_languages", None),
            ("region_countries", None),
            ("channels", transform_channels),
        ]

        for table_name, transform_func in migration_order:
            migrate_table(sqlite_conn, pg_conn, table_name, transform_func)

        # After channels are migrated, fetch valid channel_ids from PostgreSQL
        print("Fetching valid channel_ids from PostgreSQL...")
        valid_channel_ids = set()
        with pg_conn.cursor() as cursor:
            cursor.execute("SELECT channel_id FROM channels")
            for row in cursor.fetchall():
                valid_channel_ids.add(row[0])
        print(f"  Found {len(valid_channel_ids)} valid channel_ids.")

        # Migrate channel_sources with validation
        print("Migrating table: channel_sources with channel_id validation")
        sqlite_cursor = sqlite_conn.cursor()
        sqlite_cursor.execute("SELECT * FROM channel_sources")
        rows = sqlite_cursor.fetchall()

        skipped_rows_data = []

        if not rows:
            print("  No data found in channel_sources")
        else:
            columns = [description[0] for description in sqlite_cursor.description]
            placeholders = ', '.join(['%s'] * len(columns))
            columns_str = ', '.join(columns)
            insert_sql = f"INSERT INTO channel_sources ({columns_str}) VALUES ({placeholders})"
            pg_cursor = pg_conn.cursor()
            inserted_count = 0

            for row in rows:
                try:
                    row_data = list(row)
                    transformed_row_data = transform_channel_sources(row_data, columns)

                    # Validate channel_id against the set of valid_channel_ids
                    channel_id_idx = columns.index('channel_id')
                    if transformed_row_data is not None and transformed_row_data[channel_id_idx] in valid_channel_ids:
                        pg_cursor.execute(insert_sql, transformed_row_data)
                        inserted_count += 1
                    else:
                        print(f"  Skipping row in channel_sources: channel_id '{transformed_row_data[channel_id_idx] if transformed_row_data else 'N/A'}' not found in channels table. Row data: {row_data}")
                        skipped_rows_data.append(row_data)
                        continue
                except Exception as e:
                    print(f"  Error inserting row into channel_sources: {e}")
                    print(f"  Row data: {row_data}")
                    skipped_rows_data.append(row_data) # Also save rows that cause other errors
                    continue
            pg_conn.commit()
            print(f"  Migrated {inserted_count}/{len(rows)} rows for channel_sources")

        # Save skipped rows to a file
        if skipped_rows_data:
            with open(SKIPPED_CHANNELS_FILE, 'w') as f:
                json.dump(skipped_rows_data, f, indent=2)
            print(f"  Saved {len(skipped_rows_data)} skipped channel_sources rows to {SKIPPED_CHANNELS_FILE}")
        else:
            print("  No channel_sources rows were skipped.")

        # Step 5: Create foreign key constraints
        create_foreign_key_constraints(pg_conn)

        print("\nMigration completed successfully!")

    except Exception as e:
        print(f"Migration failed: {e}")
        pg_conn.rollback()
        sys.exit(1)

    finally:
        sqlite_conn.close()
        pg_conn.close()

if __name__ == "__main__":
    main()

const { PrismaClient } = require('./node_modules/.prisma/client')

async function checkMissingKeys() {
  const prisma = new PrismaClient()

  try {
    console.log('Checking countries...')
    const countries = await prisma.country.findMany({
      where: { code: { in: ['pl', 'PL', 'FR', 'ru', 'RU'] } },
    })
    console.log(
      'Found countries:',
      countries.map(c => c.code)
    )

    console.log('\nChecking languages...')
    const languages = await prisma.language.findMany({
      where: { code: { in: ['pl', 'FR', 'ru', 'pol', 'fra', 'rus'] } },
    })
    console.log(
      'Found languages:',
      languages.map(l => l.code)
    )

    // Check all countries and languages to see what we have
    console.log('\nAll countries:')
    const allCountries = await prisma.country.findMany()
    console.log('Total countries:', allCountries.length)
    console.log(
      'Sample countries:',
      allCountries.slice(0, 10).map(c => c.code)
    )

    console.log('\nAll languages:')
    const allLanguages = await prisma.language.findMany()
    console.log('Total languages:', allLanguages.length)
    console.log(
      'Sample languages:',
      allLanguages.slice(0, 10).map(l => l.code)
    )
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

checkMissingKeys()

const { PrismaClient } = require('@prisma/client')
const {
  PrismaClient: PrismaClientSqlite,
} = require('./prisma/node_modules/.prisma/client_sqlite')

async function checkMissingChannels() {
  const prismaPg = new PrismaClient()
  const prismaSqlite = new PrismaClientSqlite({
    datasources: {
      db: {
        url: 'file:./database/db.sqlite',
      },
    },
  })

  try {
    // Get some failing channel source IDs
    const failingSourceIds = [941, 1480, 1481, 3653, 3654, 5449, 5450]

    console.log('Checking channel sources and their channels...')
    for (const sourceId of failingSourceIds) {
      const channelSource = await prismaSqlite.channelSource.findUnique({
        where: { source_id: sourceId },
      })

      if (channelSource) {
        const channel = await prismaSqlite.channel.findUnique({
          where: { channel_id: channelSource.channel_id },
        })

        console.log(`Source ${sourceId}: Channel ${channelSource.channel_id}`)
        if (channel) {
          console.log(
            `  - Country: ${channel.country_code}, Language: ${channel.language_code}`
          )

          // Check if this channel exists in PostgreSQL
          const pgChannel = await prismaPg.channel.findUnique({
            where: { channel_id: channelSource.channel_id },
          })
          console.log(`  - Exists in PostgreSQL: ${pgChannel ? 'YES' : 'NO'}`)
        } else {
          console.log(`  - Channel not found in SQLite!`)
        }
      }
    }
  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prismaPg.$disconnect()
    await prismaSqlite.$disconnect()
  }
}

checkMissingChannels()
